package com.tidesquare.btms.controller.common;

import java.util.List;

import org.eclipse.microprofile.openapi.annotations.Operation;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.common.get_airport_by_code.GetAirportByCodeHandler;
import com.tidesquare.btms.controller.common.get_airports.GetAirportsHandler;
import com.tidesquare.btms.controller.common.get_airports.GetAirportsRes;
import com.tidesquare.btms.controller.common.get_countries.GetCountriesHandler;
import com.tidesquare.btms.controller.common.search_city.SearchCityHandler;
import com.tidesquare.btms.controller.common.search_usa_state.SearchUSAStateHandler;
import com.tidesquare.btms.dto.response.AirportRes;
import com.tidesquare.btms.dto.response.CityRes;
import com.tidesquare.btms.dto.response.CountryRes;
import com.tidesquare.btms.dto.response.USAStateRes;

import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

@Path("common")
@Produces(MediaType.APPLICATION_JSON)
public class CommonController {

    @Inject
    private GetCountriesHandler getCountriesHandler;

    @Inject
    private GetAirportsHandler getAirportsHandler;

    @Inject
    private SearchCityHandler searchCityHandler;

    @Inject
    private SearchUSAStateHandler searchUSAStateHandler;

    @Inject
    private GetAirportByCodeHandler getAirportByCodeHandler;

    @GET
    @Path("countries")
    @Operation(summary = "Get all countries")
    public ApiResponse<List<CountryRes>> getCountries() {
        return ApiResponse.fromData(this.getCountriesHandler.run());
    }

    @GET
    @Path("airports")
    @Operation(summary = "Get all airports")
    public ApiResponse<GetAirportsRes> getAirports() {
        return ApiResponse.fromData(this.getAirportsHandler.run());
    }

    @GET
    @Path("airports/{code}")
    @Operation(summary = "Get airport by code")
    public ApiResponse<AirportRes> getAirportByCode(@NotNull @QueryParam("code") String code) {
        return ApiResponse.fromData(this.getAirportByCodeHandler.run(code));
    }

    @GET
    @Path("cities/search")
    @Operation(summary = "Search cities")
    public ApiResponse<List<CityRes>> searchCities(@NotNull @QueryParam("keyword") String keyword, @QueryParam("countryId") Long countryId) {
        return ApiResponse.fromData(this.searchCityHandler.run(keyword, countryId));
    }

    @GET
    @Path("usa-states/search")
    @Operation(summary = "Search USA states")
    public ApiResponse<List<USAStateRes>> searchUSAStates(@NotNull @QueryParam("keyword") String keyword) {
        return ApiResponse.fromData(this.searchUSAStateHandler.run(keyword));
    }
}
